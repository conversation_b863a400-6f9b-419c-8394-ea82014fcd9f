import asyncio
from autogen_agentchat.agents   import AssistantAgent
from autogen_ext.models.openai  import OpenAIChatCompletionClient
from autogen_ext.tools.mcp      import StdioServerParams, mcp_server_tools
from autogen_core.models        import ModelInfo

async def main() -> None:
    # ① 连接 MCP Server：mcp-server-fetch
    fetch_params = StdioServerParams(
        command="uvx",
        args=["mcp-server-fetch"],
        read_timeout_seconds=60
    )
    tools = await mcp_server_tools(fetch_params)   # 自动拿到 fetch 工具

    # ② 配置本地 Qwen 模型（OpenAI-API 兼容）
    qwen_client = OpenAIChatCompletionClient(
        model="qwen-turbo",          # 按需改成你本地部署的模型名
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # Ollama / LMStudio / vLLM
        api_key="sk-85a37fd86e054d3facd52542498ea140",                      # 本地接口可随意
        model_info=ModelInfo(
            vision=False,
            function_calling =True,            # 必须支持函数调用
            json_output=False,
            family="unknown"
        )
    )

    # ③ 创建智能体：具备 MCP fetch 工具
    agent = AssistantAgent(
        name="QwenMCPFetcher",
        model_client=qwen_client,
        tools=tools,
        system_message="你是网页分析师。先用 fetch 工具抓取网页，再总结核心内容。",
        reflect_on_tool_use=True
    )

    # ④ 运行任务
    result = await agent.run(
        task="抓取 https://newsletter.victordibia.com/p/you-have-ai-fatigue-thats-why-you 并给出 3 条中文要点"
    )

    # ⑤ 输出结果
    print("【最终回答】\n", result.messages[-1].content)

if __name__ == "__main__":
    asyncio.run(main())